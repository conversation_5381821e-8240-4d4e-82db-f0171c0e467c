import { BiSolidCircleThreeQuarter } from "react-icons/bi";
import {
  FaCheckCircle,
  FaExclamationCircle,
  FaExclamationTriangle,
  FaTimesCircle,
} from "react-icons/fa";
import {
  HiAtSymbol,
  HiCloud,
  HiGlobeAlt,
  HiOutlineCube,
  HiServer,
  HiUser,
} from "react-icons/hi";
import { z } from "zod";

export const colors = [
  "bg-red-700",
  "bg-sky-700",
  "bg-green-700",
  "bg-indigo-700",
  "bg-rose-700",
  "bg-pink-700",
  "bg-teal-700",
  "bg-orange-700",
];

export function randomColor() {
  return colors[Math.floor(Math.random() * colors.length)];
}

export function getUserBgColor(userId: string): string {
  function hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    return Math.abs(hash);
  }
  const colorIndex = hashString(userId) % colors.length;
  return colors[colorIndex];
}

export const nodeTypeColors = new Map<string, string>([
  ["CLOUD_INSTANCE", "bg-sky-200"],
  ["EMAIL_ADDRESS", "bg-green-200"],
  ["PERSON", "bg-yellow-200"],
  ["URL", "bg-purple-200"],
  ["HOST", "bg-red-200"],
]);

export function capitalizeFirstLetterEachWord(word: string) {
  const uuidSchema = z.string().uuid();
  if (uuidSchema.safeParse(word).success) {
    return word;
  }
  return word
    .toLowerCase()
    .replace(/[-_]/g, " ")
    .replace(/\b\w/g, (char: string) => char.toUpperCase());
}

export function getStatusColor(status: string) {
  switch (status) {
    case "SUCCESS":
      return "bg-green-200 text-green-800";
    case "ERROR":
      return "bg-red-200 text-red-800";
    case "WARNING":
      return "bg-yellow-200 text-yellow-800";
    case "PENDING":
      return "bg-yellow-200 text-yellow-800";
    case "IN-PROGRESS":
      return "bg-sky-200 text-sky-800";
    default:
      return "";
  }
}

export function getStatusIcon(status: string) {
  switch (status) {
    case "SUCCESS":
      return <FaCheckCircle className="mr-2 h-5 w-5 text-green-600" />;
    case "ERROR":
      return <FaTimesCircle className="mr-2 h-5 w-5 text-red-600" />;
    case "WARNING":
      return <FaExclamationCircle className="mr-2 h-5 w-5 text-yellow-600" />;
    case "PENDING":
      return <FaExclamationTriangle className="mr-2 h-5 w-5 text-yellow-600" />;
    case "IN-PROGRESS":
      return (
        <BiSolidCircleThreeQuarter className="mr-2 h-4 w-4 text-sky-600" />
      );
    default:
      return null;
  }
}



export function getAwsStatusIcon(status: string) {
  let icon, tooltipText, tooltipColor;

  switch (status) {
    case "ACTIVE":
      icon = <FaCheckCircle className="text-green-500 h-4 w-4" />;
      tooltipText = "Active";
      tooltipColor = "bg-green-100 text-green-700";
      break;
    case "SUSPENDED":
      icon = <FaExclamationTriangle className="text-yellow-500 h-4 w-4" />;
      tooltipText = "Suspended";
      tooltipColor = "bg-yellow-100 text-yellow-700";
      break;
    default:
      icon = <FaCheckCircle className="text-gray-400 h-4 w-4" />;
      tooltipText = status; // Show raw status text if unrecognized
      tooltipColor = "bg-gray-100 text-gray-700";
  }

  return (
    <div className="relative flex items-center justify-center group">
      {/* Icon */}
      <div className="cursor-pointer">{icon}</div>

      {/* Tooltip */}
      <span
        className={`absolute -top-8 left-1/2 -translate-x-1/2 whitespace-nowrap rounded-md px-2 py-1 text-sm shadow-lg opacity-0 transition-opacity group-hover:opacity-100 ${tooltipColor}`}
      >
        {tooltipText}
      </span>
    </div>
  );
}

export function getAzureStatusIcon(status: string) {
  let icon, tooltipText, tooltipColor;

  switch (status) {
    case "Enabled":
      icon = <FaCheckCircle className="text-green-500 h-4 w-4" />;
      tooltipText = "Enabled";
      tooltipColor = "bg-green-100 text-green-700";
      break;
    case "Disabled":
      icon = <FaExclamationTriangle className="text-red-500 h-4 w-4" />;
      tooltipText = "Disabled";
      tooltipColor = "bg-red-100 text-red-700";
      break;
    case "Warned":
      icon = <FaExclamationTriangle className="text-yellow-500 h-4 w-4" />;
      tooltipText = "Warned";
      tooltipColor = "bg-yellow-100 text-yellow-700";
      break;
    case "PastDue":
      icon = <FaExclamationTriangle className="text-orange-500 h-4 w-4" />;
      tooltipText = "Past Due";
      tooltipColor = "bg-orange-100 text-orange-700";
      break;
    default:
      icon = <FaCheckCircle className="text-gray-400 h-4 w-4" />;
      tooltipText = status; // Show raw status text if unrecognized
      tooltipColor = "bg-gray-100 text-gray-700";
  }

  return (
    <div className="relative flex items-center justify-center group">
      {/* Icon */}
      <div className="cursor-pointer">{icon}</div>

      {/* Tooltip */}
      <span
        className={`absolute -top-8 left-1/2 -translate-x-1/2 whitespace-nowrap rounded-md px-2 py-1 text-sm shadow-lg opacity-0 transition-opacity group-hover:opacity-100 ${tooltipColor}`}
      >
        {tooltipText}
      </span>
    </div>
  );
}





export function getIconType(row: string) {
  switch (row) {
    case "CLOUD_INSTANCE":
      return <HiCloud className="h-4 w-4" />;
    case "EMAIL_ADDRESS":
      return <HiAtSymbol className="h-4 w-4" />;
    case "HOST":
      return <HiServer className="h-4 w-4" />;
    case "PERSON":
      return <HiUser className="h-4 w-4" />;
    case "URL":
      return <HiGlobeAlt className="h-4 w-4" />;
    default:
      return <HiOutlineCube className="h-6 w-6" />;
  }
}

export const cloudInstanceSvg =
  '<svg width="16" height="16" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M35.7313 18.925C35.9062 18.3063 36 17.6625 36 17C36 13.1312 32.8688 10 29 10C27.9562 10 26.9438 10.225 26 10.675C24.025 7.8625 20.7313 6 17 6C11.1 6 6.26875 10.6562 6.0125 16.5125C2.45 17.7625 0 21.1438 0 25C0 29.975 4.025 34 9 34H32C36.4188 34 40 30.425 40 26C40 23.0625 38.3875 20.325 35.7313 18.925ZM32 32H9C5.13125 32 2 28.8687 2 25C2 21.45 4.6375 18.5188 8.0625 18.0625C8.01875 17.7125 8 17.3562 8 17C8 12.0312 12.0312 8 17 8C20.7687 8 23.9937 10.3125 25.3375 13.6C26.25 12.6188 27.55 12 29 12C31.7625 12 34 14.2375 34 17C34 18.1562 33.6063 19.225 32.9437 20.075C35.8125 20.525 38 23.0063 38 26C38 29.3125 35.3125 32 32 32Z" fill="#15181B"/></svg>';

export const emailSvg =
  '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n' +
  '<path fill-rule="evenodd" clip-rule="evenodd" d="M14.5 2H1.5C0.671875 2 0 2.67188 0 3.5V12.5C0 13.3281 0.671875 14 1.5 14H14.5C15.3281 14 16 13.3281 16 12.5V3.5C16 2.67188 15.3281 2 14.5 2ZM1.5 3H14.5C14.775 3 15 3.225 15 3.5V4.79375C14.3156 5.37188 13.3375 6.16875 10.2937 8.58437C9.76562 9.00313 8.725 10.0125 8 10C7.275 10.0125 6.23125 9.00313 5.70625 8.58437C2.6625 6.16875 1.68438 5.37188 1 4.79375V3.5C1 3.225 1.225 3 1.5 3ZM14.5 13H1.5C1.225 13 1 12.775 1 12.5V6.09375C1.7125 6.67812 2.8375 7.58125 5.08437 9.36562C5.725 9.87813 6.85625 11.0063 8 11C9.1375 11.0094 10.2594 9.89062 10.9156 9.36562C13.1625 7.58125 14.2875 6.67812 15 6.09375V12.5C15 12.775 14.775 13 14.5 13Z" fill="#15181B"/>\n' +
  "</svg>\n";

export const userSvg =
  '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n' +
  '<path fill-rule="evenodd" clip-rule="evenodd" d="M10.8 9C9.90312 9 9.47188 9.5 8 9.5C6.52813 9.5 6.1 9 5.2 9C2.88125 9 1 10.8813 1 13.2V14.5C1 15.3281 1.67188 16 2.5 16H13.5C14.3281 16 15 15.3281 15 14.5V13.2C15 10.8813 13.1188 9 10.8 9ZM14 14.5C14 14.775 13.775 15 13.5 15H2.5C2.225 15 2 14.775 2 14.5V13.2C2 11.4344 3.43438 10 5.2 10C5.8125 10 6.42188 10.5 8 10.5C9.575 10.5 10.1875 10 10.8 10C12.5656 10 14 11.4344 14 13.2V14.5ZM8 8C10.2094 8 12 6.20937 12 4C12 1.79062 10.2094 0 8 0C5.79063 0 4 1.79062 4 4C4 6.20937 5.79063 8 8 8ZM8 1C9.65312 1 11 2.34687 11 4C11 5.65313 9.65312 7 8 7C6.34688 7 5 5.65313 5 4C5 2.34687 6.34688 1 8 1Z" fill="#15181B"/>\n' +
  "</svg>\n";

export const urlSvg =
  '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n' +
  '<path fill-rule="evenodd" clip-rule="evenodd" d="M8 0.25C3.71875 0.25 0.25 3.71875 0.25 8C0.25 12.2812 3.71875 15.75 8 15.75C12.2812 15.75 15.75 12.2812 15.75 8C15.75 3.71875 12.2812 0.25 8 0.25ZM14.0375 5H11.4594C11.1781 3.6125 10.7063 2.43125 10.1094 1.59063C11.8281 2.15938 13.2406 3.4 14.0375 5ZM10.75 8C10.75 8.71562 10.7 9.38125 10.6156 10H5.38438C5.3 9.38125 5.25 8.71562 5.25 8C5.25 7.28438 5.3 6.61875 5.38438 6H10.6156C10.7 6.61875 10.75 7.28438 10.75 8ZM8 1.25C8.84062 1.25 9.91875 2.62812 10.4406 5H5.55938C6.08125 2.62812 7.15938 1.25 8 1.25ZM5.89062 1.59063C5.29688 2.42813 4.82188 3.60938 4.54063 5H1.9625C2.75938 3.4 4.17188 2.15938 5.89062 1.59063ZM1.25 8C1.25 7.30312 1.35625 6.63125 1.55313 6H4.38125C4.3 6.64062 4.25 7.30625 4.25 8C4.25 8.69375 4.29688 9.35938 4.38125 10H1.55313C1.35625 9.36875 1.25 8.69688 1.25 8ZM1.9625 11H4.54063C4.82188 12.3875 5.29375 13.5688 5.89062 14.4094C4.17188 13.8406 2.75938 12.6 1.9625 11ZM8 14.75C7.15938 14.75 6.08125 13.3719 5.55938 11H10.4406C9.91875 13.3719 8.84062 14.75 8 14.75ZM10.1094 14.4094C10.7031 13.5719 11.1781 12.3906 11.4594 11H14.0375C13.2406 12.6 11.8281 13.8406 10.1094 14.4094ZM11.6188 10C11.7 9.35938 11.75 8.69375 11.75 8C11.75 7.30625 11.7031 6.64062 11.6188 6H14.4469C14.6438 6.63125 14.75 7.30312 14.75 8C14.75 8.69688 14.6438 9.36875 14.4469 10H11.6188Z" fill="#15181B"/>\n' +
  "</svg>\n";

export const hostSvg =
  '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.75 8C11.75 7.58578 12.0858 7.25 12.5 7.25C12.9142 7.25 13.25 7.58578 13.25 8C13.25 8.41422 12.9142 8.75 12.5 8.75C12.0858 8.75 11.75 8.41422 11.75 8ZM10.5 8.75C10.9142 8.75 11.25 8.41422 11.25 8C11.25 7.58578 10.9142 7.25 10.5 7.25C10.0858 7.25 9.75 7.58578 9.75 8C9.75 8.41422 10.0858 8.75 10.5 8.75ZM16 4.75C16 5.13425 15.8553 5.48459 15.6178 5.75C15.8553 6.01541 16 6.36575 16 6.75V9.25C16 9.63425 15.8553 9.98459 15.6178 10.25C15.8553 10.5154 16 10.8658 16 11.25V13.75C16 14.5784 15.3284 15.25 14.5 15.25H1.5C0.671562 15.25 0 14.5784 0 13.75V11.25C0 10.8658 0.144656 10.5154 0.38225 10.25C0.144656 9.98459 0 9.63425 0 9.25V6.75C0 6.36575 0.144656 6.01541 0.38225 5.75C0.144656 5.48459 0 5.13425 0 4.75V2.25C0 1.42156 0.671562 0.75 1.5 0.75H14.5C15.3284 0.75 16 1.42156 16 2.25V4.75ZM1 4.75C1 5.02569 1.22431 5.25 1.5 5.25H14.5C14.7757 5.25 15 5.02569 15 4.75V2.25C15 1.97431 14.7757 1.75 14.5 1.75H1.5C1.22431 1.75 1 1.97431 1 2.25V4.75ZM14.5 6.25H1.5C1.22431 6.25 1 6.47431 1 6.75V9.25C1 9.52569 1.22431 9.75 1.5 9.75H14.5C14.7757 9.75 15 9.52569 15 9.25V6.75C15 6.47431 14.7757 6.25 14.5 6.25ZM15 11.25C15 10.9743 14.7757 10.75 14.5 10.75H1.5C1.22431 10.75 1 10.9743 1 11.25V13.75C1 14.0257 1.22431 14.25 1.5 14.25H14.5C14.7757 14.25 15 14.0257 15 13.75V11.25ZM12.5 4.25C12.9142 4.25 13.25 3.91422 13.25 3.5C13.25 3.08578 12.9142 2.75 12.5 2.75C12.0858 2.75 11.75 3.08578 11.75 3.5C11.75 3.91422 12.0858 4.25 12.5 4.25ZM10.5 4.25C10.9142 4.25 11.25 3.91422 11.25 3.5C11.25 3.08578 10.9142 2.75 10.5 2.75C10.0858 2.75 9.75 3.08578 9.75 3.5C9.75 3.91422 10.0858 4.25 10.5 4.25ZM12.5 11.75C12.0858 11.75 11.75 12.0858 11.75 12.5C11.75 12.9142 12.0858 13.25 12.5 13.25C12.9142 13.25 13.25 12.9142 13.25 12.5C13.25 12.0858 12.9142 11.75 12.5 11.75ZM10.5 11.75C10.0858 11.75 9.75 12.0858 9.75 12.5C9.75 12.9142 10.0858 13.25 10.5 13.25C10.9142 13.25 11.25 12.9142 11.25 12.5C11.25 12.0858 10.9142 11.75 10.5 11.75Z" fill="#15181B" /></svg>';

export const chevronRight =
  '<svg width="4" height="4" viewBox="0 0 4 4" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"/></svg>';

export const errorCode: Record<
  number,
  { title: string; description: string; colour: string }
> = {
  400: {
    title: "Sorry, Bad Request",
    description:
      "Looks like you spoke a different language to the server. Try rephrasing your request and see if it understands you better!",
    colour: "#F59E0B",
  },
  401: {
    title: "Unauthorized!",
    description:
      "The password is probably not 'password123'. Maybe try something a little more creative?",
    colour: "#F59E0B",
  },
  403: {
    title: "Forbidden!",
    description:
      "You seem to have stumbled into a forbidden zone. No peeking! Try a different path.",
    colour: "#DC2626",
  },
  404: {
    title: "Sorry, Page Not Found",
    description:
      "We searched high and low, but this page seems to be on a permanent vacation. Try searching for something else!",
    colour: "#F59E0B",
  },
  408: {
    title: "Request Timeout!",
    description:
      "The server is on a coffee break! Please be patient and try again in a bit.",
    colour: "#F59E0B",
  },
  500: {
    title: "Internal Server Error!",
    description:
      "The server is having a meltdown. We recommend giving it some space and trying again later.",
    colour: "#DC2626",
  },
  502: {
    title: "Bad Gateway!",
    description:
      "The server you're trying to reach is having a chat with another server, but they're not speaking the same language. Try again soon!",
    colour: "#F59E0B",
  },
  503: {
    title: "Sorry, Service  Unavailable!",
    description:
      "The server is taking a well-deserved nap. Please check back later when it's fully recharged.",
    colour: "#DC2626",
  },
  504: {
    title: "Gateway Timeout!",
    description:
      "The server you're trying to reach is stuck in traffic. Give it some time to catch up and try again!",
    colour: "#F59E0B",
  },
};
