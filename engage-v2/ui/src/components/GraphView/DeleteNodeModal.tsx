import { DialogTitle } from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import Cytoscape from "cytoscape";
import { useState } from "react";
import { toast } from "react-toastify";

import {
  getGetEngagementGraphs<PERSON>uery<PERSON>ey,
  getGetEngagementQueryKey,
  useDeleteNode,
  useDeleteNodeRelationship,
} from "../../client";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import ErrorMessageModal, { ErrorMessage } from "../ErrorMessageModal";
import Modal from "../Modal";

type DeleteNodeModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  selectedNode?: Cytoscape.NodeSingular | null;
  selectedEdge?: Cytoscape.EdgeSingular | null;
  deleteType?: string;
  engagementID: string;
};

export default function DeleteNodeModal({
  isOpen,
  closeModal,
  selectedNode,
  selectedEdge,
  deleteType,
  engagementID,
}: DeleteNodeModalProps) {
  const queryClient = useQueryClient();
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);

  const deleteNodeMutation = useDeleteNode({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error removing node.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Node has been successfully removed.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
        closeModal();
      },
    },
  });

  const deleteEdgeMutation = useDeleteNodeRelationship({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error removing relationship.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Relationship has been successfully removed.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engQueryKey });
        closeModal();
      },
    },
  });

  const primaryButton: ButtonProps = {
    label: deleteType === "error" ? "Okay" : "Delete",
    onClick:
      deleteType === "error"
        ? () => {
            closeModal();
          }
        : () => {
            if (selectedNode && deleteType === "deleteNode") {
              console.log('selected Node ', selectedNode)
              deleteNodeMutation.mutate({ nodeId: selectedNode?.data("id") });
            } else if (selectedEdge && deleteType === "deleteEdge") {
              deleteEdgeMutation.mutate({
                sourceID: selectedEdge?.data("source"),
                targetID: selectedEdge?.data("target"),
              });
            }
          },
    variant: "primary",
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
    },
    variant: "secondary",
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <Modal
        title={``}
        isOpen={isOpen}
        closeModal={closeModal}
        widthClass="w-4/12"
      >
        <div className="flex flex-row justify-between">
          {deleteType === "error" ? (
            <DialogTitle
              as="h3"
              className="flex flex-row items-center justify-between space-x-12 py-6 text-xl font-semibold text-black dark:text-slate-100"
            >
              <div>
                Unable to delete selected node. There are existing relationships.
              </div>
            </DialogTitle>
          ) : (
            <DialogTitle
              as="h3"
              className="flex flex-row items-center justify-between space-x-12 pb-4 text-xl font-semibold text-black dark:text-slate-100"
            >
              <div>{`Confirm deletion of ${deleteType === "deleteEdge" ? "relationship" : "node"}`}</div>
            </DialogTitle>
          )}
        </div>
        <div className="mt-4 flex justify-end space-x-2">
          <ActionButtons
            primaryButton={primaryButton}
            {...(deleteType !== "error" && { secondaryButton })}
          />
        </div>
      </Modal>
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
