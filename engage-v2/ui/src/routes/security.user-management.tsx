import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import {
  HiMagnifyingGlass,
  HiSquares2X2,
  HiTableC<PERSON>s,
  HiUsers,
} from "react-icons/hi2";
import { IoIosInformationCircleOutline } from "react-icons/io";

import {
  getGetUserManagementUsersQueryOptions,
  getGetUsersQueryKey,
  useGetUsers,
} from "../client.ts";
import BreadCrumbs from "../components/BreadCrumbs.tsx";
import ErrorPage from "../components/ErrorHandling/ErrorPage.tsx";
import NotFound from "../components/ErrorHandling/NotFound.tsx";
import Header from "../components/Header.tsx";
import ResponsiveSideNav from "../components/ResponsiveSideNav.tsx";
import Table from "../components/Table.tsx";
import UserDetailsModal from "../components/UserManagement/UserDetailsModal.tsx";
import {
  TableRow,
  userManagementColumns,
} from "../components/UserManagement/UserManagementColumn.tsx";
import { useTheme } from "../context/ThemeProvider.tsx";
import { User } from "../model";
import { errorCode, getUserBgColor } from "../utils/assets.tsx";
import { capitalizeFirstLetter } from "../utils/validationutils.ts";

export const Route = createFileRoute("/security/user-management")({
  component: SecurityUserManagement,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetUserManagementUsersQueryOptions());
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

function SecurityUserManagement() {
  const [isCardView, setIsCardView] = useState(true);
  const { isDarkMode } = useTheme();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const { data: availableUsers } = useGetUsers({
    query: {
      queryKey: getGetUsersQueryKey(),
    },
  });

  const users = availableUsers?.users || [];

  const openUserDetailsModal = (node: TableRow) => {
    setSelectedUser(node);
    setIsModalOpen(true);
  };

  const handleInfoClick = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 p-6 dark:bg-slate-800">
          <BreadCrumbs />
          <div
            id="engagements-page-main-title"
            className="flex flex-col space-y-2 py-4"
          >
            <span className="text-3xl font-semibold text-black dark:text-white">
              User Management
            </span>
          </div>
          <div className="flex w-full flex-col justify-between py-3 md:flex-row md:items-center md:space-y-0 md:space-x-3 dark:text-white">
            <div className="order-last flex flex-row items-center rounded-sm border border-solid border-gray-200 bg-white px-4 focus-within:ring-2 focus-within:ring-purple-500 focus-within:outline-hidden md:order-none dark:bg-slate-700">
              <HiMagnifyingGlass
                className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
              />
              <input
                id="search-engagements-input"
                className="w-80 px-2 py-2 focus:outline-hidden dark:border-slate-50 dark:bg-slate-700"
                type="text"
                placeholder={"Search User"}
              />
            </div>
            <div className="order-first mb-2 flex items-center justify-end space-x-2 md:order-none md:mb-0">
              <HiSquares2X2
                onClick={() => setIsCardView(true)}
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
              />
              <HiTableCells
                onClick={() => setIsCardView(false)}
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
              />
            </div>
          </div>
          <hr className="mt-2 mb-8 h-px border-0 bg-gray-200 dark:bg-gray-700" />
          <div className="mb-4 flex flex-col items-start justify-between space-y-2 md:flex-row md:items-center md:space-y-0">
            <div className="flex items-center space-x-2">
              <HiUsers
                className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
              />
              <span className="font-semibold text-black dark:text-white">
                {users.length} Users
              </span>
            </div>
            <div className="flex w-full items-center justify-between space-x-4 md:w-max">
              {isCardView && (
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="select-all"
                    className="form-checkbox h-5 w-5"
                  />
                  <label
                    htmlFor="select-all"
                    className="text-gray-700 dark:text-white"
                  >
                    Select All
                  </label>
                </div>
              )}
              <button className="border-slate-050 rounded-md border-2 px-4 py-2 font-semibold text-gray-700 dark:text-white">
                Manage Access
              </button>
            </div>
          </div>
          <div className="md:p-4">
            {isCardView ? (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {users.map((user: User) => {
                  const bgColor = getUserBgColor(user.id);
                  return (
                    <div
                      className="relative block h-auto w-full space-y-6 overflow-hidden rounded-lg border border-gray-100 bg-white p-4 shadow-md md:max-w-[320px] dark:border-transparent dark:bg-slate-600"
                      key={user.id}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`${bgColor} h-11 w-11 cursor-pointer content-center rounded-full text-center text-white`}
                          >
                            {user.full_name
                              .split(" ")[0]
                              .charAt(0)
                              .toUpperCase()}
                          </div>
                          <div className="flex flex-col">
                            <h3 className="text-md font-bold text-gray-900 sm:text-lg dark:text-white">
                              {user.full_name}
                            </h3>
                            <span className="text-sm text-gray-400">
                              {capitalizeFirstLetter(user.app_role)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <p className="pb-8 text-sm font-semibold text-gray-700 dark:text-white">
                        <span className="text-wrap break-words text-gray-500 dark:text-gray-300">
                          {user.username}
                        </span>
                      </p>
                      <div className="absolute right-2 bottom-2 flex cursor-pointer items-center gap-1">
                        <IoIosInformationCircleOutline
                          className="h-6 w-6 opacity-70 dark:text-white"
                          onClick={() => handleInfoClick(user)}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="relative flex flex-col space-y-2">
                <div className="flex w-full flex-col rounded-lg bg-white px-3 py-3 dark:bg-[#374357b5]">
                  <div className="h-8" />
                  <Table
                    data={users}
                    columns={userManagementColumns(openUserDetailsModal)}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {selectedUser && (
        <UserDetailsModal
          selectedUser={selectedUser}
          isOpen={isModalOpen}
          closeModal={handleCloseModal}
        />
      )}
    </div>
  );
}
